import { HookContext } from '@feathersjs/feathers'

interface PopulateConfig {
  [key: string]: {
    service: string
    localKey: string
    foreignKey: string
    asArray?: boolean
    select?: string[]
  }
}

export const populate = (config: PopulateConfig) => {
  return async (context: HookContext) => {
    const { params, result, app } = context

    // Extract $populate from query params and store for processing
    const populateParam = params.query?.$populate

    // Remove $populate from query to prevent database errors
    if (populateParam) {
      delete params.query.$populate
      // Store it in params for after hook processing
      params.populateParam = populateParam
    }

    // Only process in after hooks when we have results
    if (context.type !== 'after' || !result || !params.populateParam) {
      return context
    }

    const items = Array.isArray(result) ? result :
      (result.data ? result.data : [result])

    // Process populate requests
    const populateFields = Array.isArray(params.populateParam)
      ? params.populateParam
      : [params.populateParam]

    for (const field of populateFields) {
      if (!config[field]) continue

      const populateConfig = config[field]
      const relatedService = app.service(populateConfig.service)

      // Collect all foreign keys to fetch
      const foreignKeys = items
        .map((item: any) => item[populateConfig.localKey])
        .filter((key: any) => key != null)

      if (foreignKeys.length === 0) continue

      // Fetch related records
      const query: any = {
        [populateConfig.foreignKey]: { $in: foreignKeys }
      }

      if (populateConfig.select) {
        query.$select = populateConfig.select
      }

      const relatedItems = await relatedService.find({
        query,
        paginate: false
      })

      // Map related items back to main items
      const relatedMap = new Map()
      const related = Array.isArray(relatedItems) ? relatedItems : relatedItems.data || []

      for (const relatedItem of related) {
        const key = relatedItem[populateConfig.foreignKey]
        if (populateConfig.asArray) {
          if (!relatedMap.has(key)) {
            relatedMap.set(key, [])
          }
          relatedMap.get(key).push(relatedItem)
        } else {
          relatedMap.set(key, relatedItem)
        }
      }

      // Attach populated data to items
      for (const item of items) {
        const localValue = item[populateConfig.localKey]
        if (localValue != null && relatedMap.has(localValue)) {
          item[field] = relatedMap.get(localValue)
        }
      }
    }

    return context
  }
}
