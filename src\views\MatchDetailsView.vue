<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMatchesStore } from '@/stores/matches'
import { storeToRefs } from 'pinia'
import MatchDetailsWidget from '@/components/matches/MatchDetailsWidget.vue'
import { Button } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Users, Trophy, MessageCircle, FileText } from 'lucide-vue-next'
import type { Match, MatchRegistration } from '@/api/feathers-client'

const route = useRoute()
const router = useRouter()
const matchesStore = useMatchesStore()
const { isLoading, error } = storeToRefs(matchesStore)

const matchId = computed(() => {
  const id = route.params.id
  return typeof id === 'string' ? parseInt(id, 10) : null
})

const match = ref<Match | null>(null)
const activeTab = ref('general')
const registrations = ref<MatchRegistration[]>([])
const isLoadingRegistrations = ref(false)
const registrationsError = ref<Error | null>(null)

onMounted(async () => {
  if (matchId.value) {
    try {
      match.value = await matchesStore.getMatch(matchId.value)
    } catch (err) {
      console.error('Failed to fetch match:', err)
    }
  }
})

// Watch for tab changes and fetch players when entering the players tab
watch(activeTab, async (newTab) => {
  if (newTab === 'players' && matchId.value && registrations.value.length === 0) {
    await fetchMatchRegistrations()
  }
})

async function fetchMatchRegistrations() {
  if (!matchId.value) return

  isLoadingRegistrations.value = true
  registrationsError.value = null

  try {
    // Fetch registrations with populated player data
    await matchesStore.findMatchRegistrations({
      query: {
        matchId: matchId.value,
        $populate: 'player'
      }
    })

    // Filter registrations for this match (store might have registrations for other matches)
    registrations.value = matchesStore.matchRegistrations.filter(
      reg => reg.matchId === matchId.value
    )
  } catch (err) {
    console.error('Failed to fetch match registrations:', err)
    registrationsError.value = err instanceof Error ? err : new Error('Failed to fetch registrations')
    registrations.value = []
  } finally {
    isLoadingRegistrations.value = false
  }
}

const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="min-h-screen bg-background">
    <div class="container mx-auto px-4 py-6 max-w-4xl">
      <!-- Header -->
      <div class="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="icon" @click="goBack">
          <ArrowLeft class="h-4 w-4" />
        </Button>
        <h1 class="text-2xl font-bold">Match Details</h1>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p class="text-muted-foreground">Loading match details...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="flex items-center justify-center py-12">
        <div class="text-center">
          <p class="text-red-500 mb-4">Error loading match details</p>
          <p class="text-muted-foreground">{{ error.message }}</p>
          <Button variant="outline" @click="goBack" class="mt-4">
            Go Back
          </Button>
        </div>
      </div>

      <!-- Match Details -->
      <div v-else-if="match" class="flex flex-col lg:flex-row gap-6">
        <!-- Left Side: Tabbed Content -->
        <div class="flex-1">
          <Card>
            <Tabs v-model="activeTab" default-value="general" class="w-full">
              <CardHeader class="pb-3">
                <CardTitle class="flex items-center gap-2">
                  <FileText class="h-5 w-5" />
                  {{ match.name }}
                </CardTitle>
                <TabsList class="grid w-full grid-cols-4">
                  <TabsTrigger value="general">General</TabsTrigger>
                  <TabsTrigger value="players">Players</TabsTrigger>
                  <TabsTrigger value="news">News</TabsTrigger>
                  <TabsTrigger value="scores" :disabled="(match as any).status !== 'finished'">Scores</TabsTrigger>
                </TabsList>
              </CardHeader>

              <CardContent class="space-y-4">
                <!-- General Information Tab -->
                <TabsContent value="general" class="space-y-4 mt-0">
                  <div>
                    <h3 class="text-lg font-semibold mb-2">Description</h3>
                    <div class="text-muted-foreground">
                      <p style="white-space: pre-wrap;" v-if="match.description">{{ match.description }}</p>
                      <p v-else class="italic">No description provided for this match.</p>
                    </div>
                  </div>

                  <div v-if="(match as any).rules" class="space-y-2">
                    <h3 class="text-lg font-semibold">Rules</h3>
                    <div class="text-muted-foreground">
                      <p>{{ (match as any).rules }}</p>
                    </div>
                  </div>

                  <div v-if="(match as any).location" class="space-y-2">
                    <h3 class="text-lg font-semibold">Location Details</h3>
                    <div class="text-muted-foreground">
                      <p>{{ (match as any).location }}</p>
                      <p v-if="match.address">{{ match.address }}</p>
                    </div>
                  </div>
                </TabsContent>

                <!-- Players Tab -->
                <TabsContent value="players" class="space-y-4 mt-0">
                  <div class="flex items-center gap-2 mb-4">
                    <Users class="h-5 w-5" />
                    <h3 class="text-lg font-semibold">Registered Players</h3>
                    <span v-if="registrations.length > 0" class="text-sm text-muted-foreground">({{ registrations.length }})</span>
                  </div>

                  <!-- Loading state for registrations -->
                  <div v-if="isLoadingRegistrations" class="flex items-center justify-center py-8">
                    <div class="text-center">
                      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
                      <p class="text-muted-foreground text-sm">Loading players...</p>
                    </div>
                  </div>

                  <!-- Error state for registrations -->
                  <div v-else-if="registrationsError" class="text-center py-8 text-red-500">
                    <p>Error loading players</p>
                    <p class="text-sm text-muted-foreground">{{ registrationsError.message }}</p>
                  </div>

                  <!-- Players list -->
                  <div v-else-if="registrations.length > 0" class="space-y-2">
                    <div v-for="registration in registrations" :key="registration.id" class="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p class="font-medium">
                          {{ (registration.player as any)?.name || `${(registration.player as any)?.firstname || ''} ${(registration.player as any)?.lastname || ''}`.trim() || 'Unknown Player' }}
                        </p>
                        <p class="text-sm text-muted-foreground">
                          {{ (registration.player as any)?.club || 'No club' }}
                        </p>
                      </div>
                      <div class="text-sm text-muted-foreground">
                        <div class="text-right">
                          <p>{{ registration.styleDivision || 'N/A' }}</p>
                          <p class="text-xs">{{ registration.ageDivision || '' }} {{ registration.genderDivision || '' }}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- No players state -->
                  <div v-else class="text-center py-8 text-muted-foreground">
                    <Users class="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>No players registered yet</p>
                  </div>
                </TabsContent>

                <!-- News Tab -->
                <TabsContent value="news" class="space-y-4 mt-0">
                  <div class="flex items-center gap-2 mb-4">
                    <MessageCircle class="h-5 w-5" />
                    <h3 class="text-lg font-semibold">News & Updates</h3>
                  </div>

                  <div class="text-center py-8 text-muted-foreground">
                    <MessageCircle class="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>No news or updates available</p>
                    <p class="text-sm">Check back later for match updates and announcements</p>
                  </div>
                </TabsContent>

                <!-- Scores Tab -->
                <TabsContent value="scores" class="space-y-4 mt-0">
                  <div class="flex items-center gap-2 mb-4">
                    <Trophy class="h-5 w-5" />
                    <h3 class="text-lg font-semibold">Match Results</h3>
                  </div>

                  <div v-if="(match as any).status === 'finished'">
                    <div v-if="(match as any).results && (match as any).results.length > 0" class="space-y-2">
                      <div v-for="(result, index) in (match as any).results" :key="index" class="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p class="font-medium">{{ result.playerName }}</p>
                          <p class="text-sm text-muted-foreground">{{ result.category }}</p>
                        </div>
                        <div class="text-right">
                          <p class="font-bold">{{ result.score }}</p>
                          <p class="text-sm text-muted-foreground">Rank: {{ result.rank || index + 1 }}</p>
                        </div>
                      </div>
                    </div>

                    <div v-else class="text-center py-8 text-muted-foreground">
                      <Trophy class="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>Results not available yet</p>
                    </div>
                  </div>

                  <div v-else class="text-center py-8 text-muted-foreground">
                    <Trophy class="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>Match not yet finished</p>
                    <p class="text-sm">Scores will be available after the match concludes</p>
                  </div>
                </TabsContent>
              </CardContent>
            </Tabs>
          </Card>
        </div>

        <!-- Right Side: Match Details Widget -->
        <div class="w-full lg:w-96">
          <Card>
            <MatchDetailsWidget :match="match" />
          </Card>
        </div>
      </div>

      <!-- Not Found -->
      <div v-else class="flex items-center justify-center py-12">
        <div class="text-center">
          <p class="text-muted-foreground mb-4">Match not found</p>
          <Button variant="outline" @click="goBack">
            Go Back
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
