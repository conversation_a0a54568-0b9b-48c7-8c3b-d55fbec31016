<script setup lang="ts">
import type { Match } from '@/api/feathers-client'
import { cn } from '@/lib/utils'
import { Calendar, MapPin, Users, Trophy, PawPrint } from 'lucide-vue-next'
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { storeToRefs } from 'pinia'
import { useMatchesStore } from '@/stores/matches' // Import matches store

const props = defineProps<{
  match: Match
  isSelected?: boolean // Changed from isActive to isSelected
}>()

const emit = defineEmits<{
  select: [id: number] // Changed from string to number
}>()

const authStore = useAuthStore();
const { user } = storeToRefs(authStore);
const matchesStore = useMatchesStore(); // Initialize matches store

const handleClick = () => {
  emit('select', props.match.id) // Emit number directly
}

const handleMouseEnter = () => {
  matchesStore.setHoveredMatchId(props.match.id);
};

const handleMouseLeave = () => {
  matchesStore.setHoveredMatchId(null);
};

const flagPath = computed(() => `/flags/${props.match.country?.toLowerCase()}.svg`)

const formattedDateRange = computed(() => {
  const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' }
  let startDateStr, endDateStr

  if (props.match.startDate) {
    try {
      startDateStr = new Date(props.match.startDate).toLocaleDateString(undefined, options)
    } catch { // Omitting unused error parameter
      startDateStr = 'Invalid date'
    }
  }

  if (props.match.endDate) {
    try {
      endDateStr = new Date(props.match.endDate).toLocaleDateString(undefined, options)
    } catch { // Omitting unused error parameter
      endDateStr = 'Invalid date'
    }
  }

  if (startDateStr && endDateStr) {
    if (startDateStr === endDateStr) {
      return startDateStr
    }
    return `${startDateStr} - ${endDateStr}`
  } else if (startDateStr) {
    return startDateStr
  } else if (endDateStr) {
    // This case might be unusual (end date without start date) but handled
    return `Ends: ${endDateStr}`
  }
  return 'Date N/A'
})

// Computed properties based on match prop

const getMatchStatus = (match: Match): string => {
  if (!match.startDate) return 'Date N/A'

  const today = new Date()
  today.setHours(0, 0, 0, 0)

  let startDate: Date
  try {
    startDate = new Date(match.startDate)
    startDate.setHours(0, 0, 0, 0)
  } catch {
    return 'Invalid Start Date'
  }

  let endDate: Date | null = null
  if (match.endDate) {
    try {
      endDate = new Date(match.endDate)
      endDate.setHours(0, 0, 0, 0)
    } catch {
      // Non-critical, can proceed without end date
    }
  }

  const diffTimeStart = startDate.getTime() - today.getTime()
  const diffDaysStart = Math.ceil(diffTimeStart / (1000 * 60 * 60 * 24))

  if (diffDaysStart > 0) {
    return `${diffDaysStart} day${diffDaysStart === 1 ? '' : 's'}`
  }
  // Match starts today or has already started
  if (endDate) {
    if (endDate.getTime() < today.getTime()) {
      return 'Finished' // Ended before today
    }
    return 'In progress' // Started and ends today or in the future
  }
  // No end date, or end date is invalid
  if (diffDaysStart === 0) {
    return 'In progress' // Starts today, no end date specified or end date is in the future
  }
  // Started in the past, no end date specified or end date is in the future
  return 'In progress'
}

const matchStatusDisplay = computed(() => getMatchStatus(props.match));

const distance = computed(() => {
  // Support both latitude/longitude and lat/lng for player
  const playerLat =
    typeof user.value?.player?.latitude === 'number'
      ? user.value.player.latitude
      : typeof user.value?.player?.lat === 'number'
      ? user.value.player.lat
      : undefined;
  const playerLng =
    typeof user.value?.player?.longitude === 'number'
      ? user.value.player.longitude
      : typeof user.value?.player?.lng === 'number'
      ? user.value.player.lng
      : undefined;
  const matchLat = props.match.latitude;
  const matchLng = props.match.longitude;

  if (
    typeof playerLat !== 'number' ||
    typeof playerLng !== 'number' ||
    typeof matchLat !== 'number' ||
    typeof matchLng !== 'number'
  ) {
    return 'N/A';
  }

  const R = 6371; // Radius of the Earth in km
  const dLat = deg2rad(matchLat - playerLat);
  const dLon = deg2rad(matchLng - playerLng);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(playerLat)) *
    Math.cos(deg2rad(matchLat)) *
    Math.sin(dLon / 2) *
    Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distanceKm = R * c;
  return `${distanceKm.toFixed(0)} km`;
})

function deg2rad(deg: number) {
  return deg * (Math.PI / 180);
}

const currentParticipants = computed(() => {

  return 0
})

</script>

<template>
  <div
    :class="cn(
      'flex flex-col gap-2 rounded-lg border p-3 text-left text-sm transition-all', // Base classes, hover removed from here
      (props.match as any).read ? 'bg-card' : 'bg-primary/5', // Read status background
      (props.match as any).isHighlighted && 'border-l-4 border-l-primary', // Highlighted border
      // Selection styling: applies bluish bg if selected, or default hover if not selected
      props.isSelected
        ? 'bg-blue-500/10' // Selected: bluish background, persists on hover
        : 'hover:bg-accent/20' // Not selected: default hover effect
    )"
    role="button"
    tabindex="0"
    @click="handleClick"
    @keydown.enter="handleClick"
    @keydown.space="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="flex items-center justify-between">
      <h3 class="font-semibold">{{ match.name }}</h3>
      <span class="text-sm text-primary">{{ matchStatusDisplay }}</span>
    </div>

    <div class="flex items-center gap-1.5">
      <div class="flex h-4 w-4 items-center justify-center">
        <img :src="flagPath" :alt="match.country + ' flag'" class="h-3" />
      </div>
      <span class="flex items-center gap-1">
        {{ match.country }} {{ match.city }}
      </span>
    </div>

    <div class="flex items-center gap-1.5">
      <Calendar class="h-4 w-4 text-muted-foreground" />
      <span>{{ formattedDateRange }}</span>
    </div>

    <!-- Responsive row for PawPrint, tournament, distance, participants -->
    <div class="flex flex-col gap-2 mt-1 sm:flex-row sm:items-center sm:justify-between sm:gap-4">
      <div class="flex items-center gap-2">
        <PawPrint class="h-4 w-4 text-emerald-600" />
        <div v-if="match.tournament" class="flex items-center gap-1">
          <Trophy class="h-4 w-4 text-yellow-500" />
          <span>{{ match.tournament.name }}</span>
        </div>
      </div>
      <div class="flex items-center gap-4">
        <div class="flex items-center gap-1.5">
          <MapPin class="h-4 w-4 text-muted-foreground" />
          <span>{{ distance }}</span>
        </div>
        <div class="flex items-center gap-1.5">
          <Users class="h-4 w-4 text-muted-foreground" />
          <span>{{ currentParticipants }}/{{ props.match.maxPlayersAmount ?? 'N/A' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
