<script setup lang="ts">
// Existing imports from MatchesContainer
import { ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable'
import { Separator } from '@/components/ui/separator'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { refDebounced, useWindowSize } from '@vueuse/core'
import {
  Search,
  Filter,
  MapPin,
  Users,
  Calendar as CalendarIcon // Renamed to avoid conflict with EventCalendar component
} from 'lucide-vue-next'
import { computed, ref, watch, onMounted } from 'vue' // Added onMounted
import MatchesList from './MatchesList.vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { useMatchesStore } from '@/stores/matches'
import { storeToRefs } from 'pinia'
import { useAuthStore } from '@/stores/auth'; // Import auth store
import { useI18n } from 'vue-i18n'

// Imports moved from MatchesView.vue
import type { DateRange, DateValue } from 'reka-ui' // Assuming reka-ui is a globally available type or installed package
import EventCalendar from '@/components/EventCalendar.vue'
import ArcheryMap from '@/components/map/ArcheryMap.vue'
import MatchDetailsWidget from '@/components/matches/MatchDetailsWidget.vue'
import {
  SidebarInset,
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarTrigger,
} from '@/components/ui/sidebar'
import type { Match } from '@/api/feathers-client'

interface MatchesProps {
  defaultLayout?: number[]
  navCollapsedSize?: number
}

const props = withDefaults(defineProps<MatchesProps>(), {
  defaultLayout: () => [265, 655],
  navCollapsedSize: 44
})

const { t } = useI18n()
const matchesStore = useMatchesStore()
const authStore = useAuthStore(); // Initialize auth store
const {
  matches,
  isLoading,
  error,
  currentMatch,
  filteredMatches: filteredMatchListFromStore
} = storeToRefs(matchesStore)
const { user } = storeToRefs(authStore); // Get user for "Near me" filter

const searchValue = ref('')
const debouncedSearch = refDebounced(searchValue, 250)

watch(debouncedSearch, (newQuery) => {
  matchesStore.setSearchQuery(newQuery || '')
})

onMounted(() => {
  matchesStore.findMatches({query: {
    $limit: 50,
    isActive: true,
  }})
})

const mapLatitude = computed(() => currentMatch.value?.latitude)
const mapLongitude = computed(() => currentMatch.value?.longitude)

const selectedCalendarDateRange = ref<DateRange | undefined>()
const hoveredMatches = ref<Match[]>([])

function handleCalendarDateRangeUpdate(value: DateRange | undefined) {
  selectedCalendarDateRange.value = value
}

function handleCalendarDateHover(matches: Match[]) {
  hoveredMatches.value = matches
}

function handleCalendarDateUnhover() {
  hoveredMatches.value = []
}

function handleCalendarSelectMatch(matchId: number) {
  matchesStore.selectMatch(matchId)
}

const calendarMatches = computed(() => matches.value as Match[] || [])

const convertDateValueToJSDate = (dateValue: DateValue | undefined | null): Date | null => {
  if (!dateValue) return null;
  if (dateValue && typeof (dateValue as any).toDate === 'function') {
    return (dateValue as any).toDate('UTC'); // Or your specific timezone
  }
  // Fallback for ISO strings or other direct constructor-compatible values if necessary
  const d = new Date(dateValue.toString()); // Ensure it's a string if not a direct Date constructor type
  if (!isNaN(d.getTime())) return d;
  return null;
};

const matchesFilteredByCalendar = computed(() => {
  const baseMatches = filteredMatchListFromStore.value;
  if (!selectedCalendarDateRange.value || (!selectedCalendarDateRange.value.start && !selectedCalendarDateRange.value.end)) {
    return baseMatches;
  }

  const { start, end } = selectedCalendarDateRange.value;

  return baseMatches.filter(match => {
    if (!match.startDate) return false;
    const matchDate = new Date(match.startDate);
    matchDate.setHours(0, 0, 0, 0);

    const startDate = convertDateValueToJSDate(start);
    const endDate = convertDateValueToJSDate(end);

    if (startDate && endDate) {
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(0, 0, 0, 0);
      return matchDate >= startDate && matchDate <= endDate;
    } else if (startDate) {
      startDate.setHours(0, 0, 0, 0);
      return matchDate.getTime() === startDate.getTime();
    }
    return true;
  });
});

const showFilters = ref(true);
const filterThisWeek = ref(false);
const filterNextMonth = ref(false);
const filterNext3Months = ref(false);
const filterNext6Months = ref(false);
const filterThisYear = ref(false);
const filterNearMe = ref(false);
const filterAvailable = ref(false);
const nearMeDistance = ref([50]); // Distance in kilometers, default 50km

const toggleFiltersVisibility = () => {
  showFilters.value = !showFilters.value;
};

const toggleThisWeekFilter = () => {
  filterThisWeek.value = !filterThisWeek.value;
};
const toggleNextMonthFilter = () => {
  filterNextMonth.value = !filterNextMonth.value;
};
const toggleNext3MonthsFilter = () => {
  filterNext3Months.value = !filterNext3Months.value;
};
const toggleNext6MonthsFilter = () => {
  filterNext6Months.value = !filterNext6Months.value;
};
const toggleThisYearFilter = () => {
  filterThisYear.value = !filterThisYear.value;
};
const toggleNearMeFilter = () => {
  filterNearMe.value = !filterNearMe.value;
};
const toggleAvailableFilter = () => {
  filterAvailable.value = !filterAvailable.value;
};

// Helper function for distance calculation (Haversine)
function deg2rad(deg: number) {
  return deg * (Math.PI / 180);
}

const badgeFilteredMatches = computed(() => {
  let itemsToFilter = matchesFilteredByCalendar.value;

  // "This week" filter
  if (filterThisWeek.value) {
    const today = new Date();
    const currentDay = today.getDay(); // 0 (Sun) - 6 (Sat)
    const firstDayOfWeek = new Date(today);
    // Adjust to Monday (if Sunday, go back 6 days, else go back currentDay - 1 days)
    firstDayOfWeek.setDate(today.getDate() - (currentDay === 0 ? 6 : currentDay - 1));
    firstDayOfWeek.setHours(0, 0, 0, 0);

    const lastDayOfWeek = new Date(firstDayOfWeek);
    lastDayOfWeek.setDate(firstDayOfWeek.getDate() + 6);
    lastDayOfWeek.setHours(23, 59, 59, 999);

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = new Date(match.startDate);
      return matchStartDate >= firstDayOfWeek && matchStartDate <= lastDayOfWeek;
    });
  }

  // "Next month" filter
  if (filterNextMonth.value) {
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
    nextMonth.setHours(0, 0, 0, 0);

    const endOfNextMonth = new Date(today.getFullYear(), today.getMonth() + 2, 0);
    endOfNextMonth.setHours(23, 59, 59, 999);

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = new Date(match.startDate);
      return matchStartDate >= nextMonth && matchStartDate <= endOfNextMonth;
    });
  }

  // "Next 3 months" filter
  if (filterNext3Months.value) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const next3Months = new Date(today);
    next3Months.setMonth(today.getMonth() + 3);
    next3Months.setHours(23, 59, 59, 999);

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = new Date(match.startDate);
      return matchStartDate >= today && matchStartDate <= next3Months;
    });
  }

  // "Next 6 months" filter
  if (filterNext6Months.value) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const next6Months = new Date(today);
    next6Months.setMonth(today.getMonth() + 6);
    next6Months.setHours(23, 59, 59, 999);

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = new Date(match.startDate);
      return matchStartDate >= today && matchStartDate <= next6Months;
    });
  }

  // "This year" filter
  if (filterThisYear.value) {
    const today = new Date();
    const startOfYear = new Date(today.getFullYear(), 0, 1);
    startOfYear.setHours(0, 0, 0, 0);

    const endOfYear = new Date(today.getFullYear(), 11, 31);
    endOfYear.setHours(23, 59, 59, 999);

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = new Date(match.startDate);
      return matchStartDate >= startOfYear && matchStartDate <= endOfYear;
    });
  }

  // "Near me" filter
  if (filterNearMe.value) {
    // Support both latitude/longitude and lat/lng for player
    const playerLat =
      typeof user.value?.player?.latitude === 'number'
        ? user.value.player.latitude
        : typeof user.value?.player?.lat === 'number'
        ? user.value.player.lat
        : undefined;
    const playerLng =
      typeof user.value?.player?.longitude === 'number'
        ? user.value.player.longitude
        : typeof user.value?.player?.lng === 'number'
        ? user.value.player.lng
        : undefined;

    if (typeof playerLat === 'number' && typeof playerLng === 'number') {
      const nearRadiusKm = nearMeDistance.value[0]; // Use dynamic distance from slider

      itemsToFilter = itemsToFilter.filter(match => {
        if (typeof match.latitude !== 'number' || typeof match.longitude !== 'number') return false;
        const R = 6371; // Radius of the Earth in km
        const dLat = deg2rad(match.latitude - playerLat);
        const dLon = deg2rad(match.longitude - playerLng);
        const a =
          Math.sin(dLat / 2) * Math.sin(dLat / 2) +
          Math.cos(deg2rad(playerLat)) *
          Math.cos(deg2rad(match.latitude)) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distanceKm = R * c;
        return distanceKm <= nearRadiusKm;
      });
    }
  }

  // "Available" filter
  if (filterAvailable.value) {
    const today = new Date();
    today.setHours(0,0,0,0); // Compare with start of today

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = new Date(match.startDate);
      const isUpcoming = matchStartDate >= today;

      let registrationOpen = true;
      if (match.registrationEnds) {
        const matchRegEndDate = new Date(match.registrationEnds);
        registrationOpen = matchRegEndDate >= today;
      }
      // Consider registrationFinished, assuming it's a boolean.
      // If it can be null/undefined, adjust accordingly.
      const notFinished = match.registrationFinished !== true;

      return isUpcoming && registrationOpen && notFinished;
    });
  }

  return itemsToFilter;
});

const upcomingMatchList = computed(() =>
  badgeFilteredMatches.value.filter(item => {
    if (!item.startDate) return false;
    const startDate = new Date(item.startDate);
    const today = new Date();
    today.setHours(0,0,0,0);
    return startDate >= today;
  })
);

const tournamentMatchList = computed(() =>
  badgeFilteredMatches.value.filter(item => item.tournamentConfirmed === true)
);

const handleSelectMatchFromMap = (matchId: string) => {
  // Corrected to use selectMatch and parse the ID to a number
  matchesStore.selectMatch(parseInt(matchId, 10));
};

// Responsive sidebar styles using VueUse
const { width } = useWindowSize();

const sidebarStyles = computed(() => {
  const styles = (() => {
    if (width.value >= 1280) {
      // xl and above: 25rem
      return {
        '--sidebar-width': '25rem',
        '--sidebar-width-mobile': '25rem'
      };
    } else if (width.value >= 768) {
      // md to xl: 15rem
      return {
        '--sidebar-width': '15rem',
        '--sidebar-width-mobile': '25rem'
      };
    }
    // Default/mobile: 25rem
    return {
      '--sidebar-width': '25rem',
      '--sidebar-width-mobile': '25rem'
    };
  })();

  // Debug logging
  console.log(`Window width: ${width.value}px, Sidebar width: ${styles['--sidebar-width']}`);

  return styles;
});

</script>

<template>
  <SidebarProvider :style="sidebarStyles">
    <SidebarInset>
      <div class="flex flex-col gap-4">
        <div v-if="isLoading" class="text-center p-4">
          Loading matches...
        </div>
        <div v-else-if="error" class="text-center p-4 text-red-500">
          Error loading matches: {{ error?.message }}
        </div>
        <div v-else>


              <Tabs default-value="all" class="h-full">
                <div class="flex items-center gap-2 px-4 py-2">
                  <!-- Mobile sidebar trigger -->
                  <SidebarTrigger class="md:hidden" />

                  <TabsList class="">
                    <TabsTrigger value="all" class="">
                      {{ t('navigation.matches') }} <Badge class="ml-2" variant="outline">{{ badgeFilteredMatches.length }}</Badge>
                    </TabsTrigger>
                    <TabsTrigger value="tournament" class="text-zinc-600 dark:text-zinc-200">
                      {{ t('filters.tournaments') }} <Badge class="ml-2" variant="outline">{{ tournamentMatchList.length }}</Badge>
                    </TabsTrigger>

                  </TabsList>
                </div>


                <div class="bg-background/95 p-4 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                  <div class="flex items-center gap-2">
                    <div class="relative flex-1">
                      <Search class="absolute left-2 top-2.5 size-4 text-muted-foreground" />
                      <Input v-model="searchValue" placeholder="Search matches..." class="pl-8" />
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      :class="{ 'bg-primary text-primary-foreground': showFilters }"
                      @click="toggleFiltersVisibility"
                    >
                      <Filter class="h-4 w-4" />
                      <span class="sr-only">{{ t('filters.toggleFilters') }}</span>
                    </Button>
                  </div>

                  <div v-if="showFilters" class="mt-2 space-y-2">
                    <!-- Time-based filters -->
                    <div class="flex items-center justify-start gap-2 overflow-x-auto pb-2">
                      <Badge
                        variant="outline"
                        class="flex items-center gap-1 cursor-pointer"
                        :class="{ 'border-primary text-primary': filterThisWeek }"
                        @click="toggleThisWeekFilter"
                      >
                        <CalendarIcon class="h-3 w-3" />
                        <span>{{ t('filters.thisWeek') }}</span>
                      </Badge>
                      <Badge
                        variant="outline"
                        class="flex items-center gap-1 cursor-pointer"
                        :class="{ 'border-primary text-primary': filterNextMonth }"
                        @click="toggleNextMonthFilter"
                      >
                        <CalendarIcon class="h-3 w-3" />
                        <span>{{ t('filters.nextMonth') }}</span>
                      </Badge>
                      <Badge
                        variant="outline"
                        class="flex items-center gap-1 cursor-pointer"
                        :class="{ 'border-primary text-primary': filterNext3Months }"
                        @click="toggleNext3MonthsFilter"
                      >
                        <CalendarIcon class="h-3 w-3" />
                        <span>{{ t('filters.next3Months') }}</span>
                      </Badge>
                      <Badge
                        variant="outline"
                        class="flex items-center gap-1 cursor-pointer"
                        :class="{ 'border-primary text-primary': filterNext6Months }"
                        @click="toggleNext6MonthsFilter"
                      >
                        <CalendarIcon class="h-3 w-3" />
                        <span>{{ t('filters.next6Months') }}</span>
                      </Badge>
                      <Badge
                        variant="outline"
                        class="flex items-center gap-1 cursor-pointer"
                        :class="{ 'border-primary text-primary': filterThisYear }"
                        @click="toggleThisYearFilter"
                      >
                        <CalendarIcon class="h-3 w-3" />
                        <span>{{ t('filters.thisYear') }}</span>
                      </Badge>
                    </div>

                    <!-- Location and availability filters -->
                    <div class="flex items-center justify-start gap-2 overflow-x-auto pb-2">
                      <Badge
                        variant="outline"
                        class="flex items-center gap-1 cursor-pointer"
                        :class="{ 'border-primary text-primary': filterNearMe }"
                        @click="toggleNearMeFilter"
                      >
                        <MapPin class="h-3 w-3" />
                        <span>{{ t('filters.nearMe') }}</span>
                      </Badge>

                      <!-- Inline distance slider for "Near me" filter -->
                      <div v-if="filterNearMe" class="flex items-center gap-2 px-2 py-1 bg-muted/30 rounded-md min-w-0">
                        <span class="text-xs text-muted-foreground whitespace-nowrap">{{ nearMeDistance[0] }}km</span>
                        <Slider
                          v-model="nearMeDistance"
                          :min="5"
                          :max="500"
                          :step="5"
                          class="w-20"
                        />
                      </div>

                      <Badge
                        variant="outline"
                        class="flex items-center gap-1 cursor-pointer"
                        :class="{ 'border-primary text-primary': filterAvailable }"
                        @click="toggleAvailableFilter"
                      >
                        <Users class="h-3 w-3" />
                        <span>{{ t('filters.available') }}</span>
                      </Badge>
                    </div>
                  </div>
                </div>

                <TabsContent value="all" class="h-[calc(100%-10rem)] overflow-auto">
                  <MatchesList :items="badgeFilteredMatches" :hovered-matches="hoveredMatches" />
                </TabsContent>
                <TabsContent value="tournament" class="h-[calc(100%-10rem)] overflow-auto">
                  <MatchesList :items="tournamentMatchList" :hovered-matches="hoveredMatches" />
                </TabsContent>
              </Tabs>

        </div>
      </div>
    </SidebarInset>
    <Sidebar
      class="sticky top-0 h-svh border-l"
      collapsible="offcanvas"
    >
      <SidebarContent class="flex flex-col gap-0">
        <div class="mb-0 border-b border-sidebar-border pb-0">
          <MatchDetailsWidget :match="currentMatch" />
        </div>

        <div class="mb-0 border-b border-sidebar-border pb-0">
          <div class="h-52">
            <ArcheryMap
              :latitude="mapLatitude"
              :longitude="mapLongitude"
              :matches-to-display="badgeFilteredMatches"
              :hovered-matches="hoveredMatches"
              @select-match="handleSelectMatchFromMap"
            />
          </div>
        </div>

        <div v-if="!currentMatch" class="flex-1">
          <EventCalendar
            :selected-date-range="selectedCalendarDateRange"
            :matches="calendarMatches"
            @update:selected-date-range="handleCalendarDateRangeUpdate"
            @hover:date="handleCalendarDateHover"
            @unhover:date="handleCalendarDateUnhover"
            @select:match="handleCalendarSelectMatch"
          />
        </div>
      </SidebarContent>
    </Sidebar>
  </SidebarProvider>
</template>
