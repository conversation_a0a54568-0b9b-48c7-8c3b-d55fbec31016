// src/stores/tournaments.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { app, type Tournament } from '@/api/feathers-client'

export const useTournamentsStore = defineStore('tournaments', () => {
  const tournaments = ref<Record<number, Tournament>>({})
  const isLoading = ref(false)
  const error = ref<Error | null>(null)

  async function fetchTournament(id: number) {
    isLoading.value = true
    error.value = null
    try {
      const data = await app.service('tournaments').get(id)
      tournaments.value[id] = data
    } catch (e: any) {
      error.value = e
    } finally {
      isLoading.value = false
    }
  }

  return { tournaments, isLoading, error, fetchTournament }
})
